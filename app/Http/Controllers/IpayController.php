<?php

namespace App\Http\Controllers;

use App\Http\Requests\IpayPostbackRequest;
use App\Models\Cashflow;
use App\Models\goods;
use App\Models\IpayTransaction;
use App\Models\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Storage;

class IpayController extends Controller
{
    public function token(): string
    {
        $response = Http::asForm()->withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Accept' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(config('ipay.client_id') . ':' . config('ipay.secret_key'))
        ])->post(config('ipay.bog_url') . "/oauth2/token", [
            'grant_type' => 'client_credentials'
        ]);

        if (!$response->ok()) {
            Log::alert('ipay-token-error', [$response->body()]);
            abort(500);
        }

        return $response->json()['access_token'];
    }

    public function initiate(Request $request)
    {
        if(!auth()->check())
        {
            return redirect('login');
        }

        $validated = $request->validate([
            "topUpAmount" => "numeric|required"
        ]);

        $access_token = $this->token();

        $transaction_id = "BOG" . bin2hex(random_bytes(20));
        $transaction = IpayTransaction::query()->create([
            'user_id' => auth()->id(),
            'shop_order_id' => $transaction_id,
            'amount' => $validated['topUpAmount']
        ]);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $access_token
        ])->post(config('ipay.bog_url') . "/checkout/orders", [
            'intent' => 'CAPTURE',
            'locale' => 'ka',
            'shop_order_id' => $transaction->shop_order_id,
            'redirect_url' => $request->getSchemeAndHttpHost() . '/user/ipay/postback',
            'show_shop_order_id_on_extract' => 'true',
            'capture_method' => 'AUTOMATIC',
            'purchase_units' => [
                [
                    "amount" => [
                        "currency_code" => "GEL",
                        "value" => $transaction->amount
                    ],
                ]
            ],
            'items' => [

            ]
        ]);

        if (!$response->successful())
        {
            Log::error('bog.checkout.api.error', [$response->body()]);
        }

        $transaction->status = $response->json()['status'];
        $transaction->payment_hash = $response->json()['payment_hash'];
        $transaction->order_id = $response->json()['order_id'];
        $transaction->save();

        session([
            'payment_hash' => $transaction->payment_hash,
            'order_id' => $transaction->order_id,
        ]);
        session()->save();

        $link = $response->json()['links'][1]['href'];

        return redirect($link);
    }

    public function postback(Request $request)
    {
        // sheamowme tranzaqcia da tu gadaxdilia gaushvi successze
        if (!$request->has("status")) {
            if (session()->has('order_id')) {
                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $this->token()
                ])->get(config('ipay.bog_url') . '/checkout/payment/'.session()->get('order_id'));
                if (!$response->successful())
                {
                    Log::error('ipay-postback-error', [$response->body()]);
                }
                else
                {
                    if ($response->json()['status'] == IpayTransaction::SUCCESS) {

                        $transaction = IpayTransaction::where("order_id", $response->json()['order_id'])->first();
                        if (!empty($transaction) && !$transaction->is_sanex_processed) {
                            $transaction->status = $response->json()['status'];
                            $transaction->status_description = $response->json()['status_description'];
                            $transaction->ipay_payment_id = $response->json()['ipay_payment_id'];
                            $transaction->payment_method = $response->json()['payment_method'];
                            $transaction->card_type = $response->json()['card_type'];
                            $transaction->is_sanex_processed = true;
                            $transaction->save();

                            $user = User::find($transaction->user_id);
                            $user->balance += $transaction->amount;
                            $user->save();

                            $cashflow = Cashflow::create([
                                "user_id" => $user->id,
                                "amount" => $transaction->amount,
                                "is_income" => true,
                                "payment_source" => "bog"
                            ]);
                            $cashflow->save();

                            //ჩემი დამატებული
                            $user_id2 = $user;
                            //        goods that must be payed.
                            $goods1 = goods::where(["user_id" => $user_id2, "is_payed" => 0])->get();
                            foreach ($goods1 as $good1) {
                                if ($user->balance >= 0) {
                                    $good1->is_payed = 1;
                                    $good1->save();
                                }
                            }
                            //ჩემი დამატებული

                            return view("customer.bog.success", compact("user", "transaction"));
                            // Mark things in your database as paid
                        } else return redirect(route("customer.redirect"));


                    }
                }
            }
        }


    }

    public function post (IpayPostbackRequest $request) {

        $myfile = file_put_contents('logs.txt', print_r($request->all()).PHP_EOL , FILE_APPEND | LOCK_EX);

        if ($request->input("status") == IpayTransaction::SUCCESS) {
            $transaction = IpayTransaction::where("order_id", $request->input("order_id"))->first();
            if (!empty($transaction) && !$transaction->is_sanex_processed) {
                $transaction->status = $request->input('status');
                $transaction->status_description = $request->input('status_description');
                $transaction->ipay_payment_id = $request->input('ipay_payment_id');
                $transaction->payment_method = $request->input('payment_method');
                $transaction->card_type = $request->input('card_type');
                $transaction->is_sanex_processed = true;
                $transaction->save();

                $user = User::find($transaction->user_id);
                $user->balance += $transaction->amount;
                $user->save();

                $cashflow = Cashflow::create([
                    "user_id" => $user->id,
                    "amount" => $transaction->amount,
                    "is_income" => true,
                    "payment_source" => "bog"
                ]);
                $cashflow->save();


                //ჩემი დამატებული
                $user_id2 = $user;
                //        goods that must be payed.
                $goods1 = goods::where(["user_id" => $user_id2, "is_payed" => 0])->get();
                foreach ($goods1 as $good1) {
                    if ($user->balance >= 0) {
                        $good1->is_payed = 1;
                        $good1->save();
                    }
                }
                //ჩემი დამატებული

                return response("OK",200);
                // Mark things in your database as paid
            } else return response("Already processed",401);

        } else {
            return response("OK",200);
        }
    }

    public function refund(Request $request)
    {
        dd($request->all());
    }


}
