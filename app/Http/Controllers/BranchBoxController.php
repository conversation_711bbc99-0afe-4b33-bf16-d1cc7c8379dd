<?php

namespace App\Http\Controllers;

use App\Http\Requests\BranchBox\AddContainerRequest;
use App\Http\Requests\BranchBox\BoxCreateRequest;
use App\Http\Requests\BranchBox\BoxEditRequest;
use App\Http\Requests\BranchBox\BoxFillRequest;
use App\Http\Requests\BranchBox\DeliveryEditRequest;
use App\Http\Requests\BranchBox\LocalDistribution\CreateRequest;
use App\Http\Requests\BranchBox\LocalDistribution\EditRequest;
use App\Models\Branch;
use App\Models\BranchBox;
use App\Models\BranchBoxContainer;
use App\Models\BranchBoxDelivery;
use App\Models\BranchBoxDeliveryStatus;
use App\Models\Driver;
use App\Models\Flight;
use App\Models\goods;
use App\Models\LocalDistribution;
use App\Models\UserBranchChangeLog;
use App\Support\SMS;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\Label\Font\NotoSans;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class BranchBoxController extends Controller
{
//    public function __construct(public BranchService $service)
//    {
//        //
//    }

    public function flightDelivery(Request $request, $id)
    {
        if ($request->ajax()) {
            $goods = Goods::query()
                ->with(['Branch', 'localDistribution.branchBox'])
                ->where('flight_id', $id)
                ->when($request->get('branch_id'), function ($query, $branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->when($request->get('scanned'), function ($query, $scanned) {
                    $query->where('small_comment', $scanned == 1 ? 'SCANNED' : null);
                })
                ->when($request->has('on_shelf'), function ($query) use ($request) {
                    if ($request->on_shelf == 1) {
                        $query->whereHas('localDistribution.branchBox');
                    } elseif ($request->on_shelf == 2) {
                        $query->whereDoesntHave('localDistribution.branchBox');
                    }
                })
                ->when($request->get('transform') != 0, function ($query) use ($request, $id) {
                    if ($request->get('transform') == 1) {
                        // გამოვყოთ მხოლოდ ის, ვინც არაა არც კონტეინერში და არც branchBox-ში
                        $query->whereDoesntHave('localDistribution.branchBox', function ($q) use ($request) {
                            if ($request->get('branch_id')) {
                                $q->where('branch_id', $request->get('branch_id'));
                            }
                        })->whereDoesntHave('containerDelivery', function ($q) use ($request, $id) {
                            $q->where('flight_id', $id);
                            if ($request->get('branch_id')) {
                                $q->where('branch_id', $request->get('branch_id'));
                            }
                        });
                    } else {
                        // გამოვყოთ მხოლოდ ის, ვინც არის ან კონტეინერში ან branchBox-ში
                        $query->where(function ($q) use ($request, $id) {
                            $q->whereHas('localDistribution.branchBox', function ($sub) use ($request) {
                                if ($request->get('branch_id')) {
                                    $sub->where('branch_id', $request->get('branch_id'));
                                }
                            })->orWhereHas('containerDelivery', function ($sub) use ($request, $id) {
                                $sub->where('flight_id', $id);
                                if ($request->get('branch_id')) {
                                    $sub->where('branch_id', $request->get('branch_id'));
                                }
                            });
                        });
                    }
                });

            return response()->json(['data' => $goods->get()]);
        }

        $branches = Branch::all();
        $flight = Flight::find($id);

        return view('admin.flights.delivery.parcels', compact('id', 'branches', 'flight'));
    }

    public function create()
    {
        return view('admin.box_section.create');
    }

    public function createSection()
    {
        $branches = Branch::all();
        return view('admin.box.create', compact('branches'));

    }

    public function boxCreate(BoxCreateRequest $request)
    {
        $exists = BranchBox::query()
            ->where('name', $request->name)
            ->where('branch_id', $request->branch_id)
            ->exists()
        ;
        if ($exists){
            return redirect()->back()->with('error', 'ჩანაწერი უკვე არსებობს');
        }
        BranchBox::query()->create($request->validated());
        if($request->post('branch_id') == null){
            return redirect()->route('branch.boxes.box.list')->with('success', 'წარმატებით დაემატა');
        }
        return redirect()->route('branch.boxes.box.listSection')->with('success', 'წარმატებით დაემატა');
    }

    public function list(Request $request)
    {
        $data = BranchBox::query()
            ->when($request->keyword, function ($q) use ($request) {
                return $q->where('name', $request->keyword);
            })
            ->where('branch_id','=',null)
            ->get();
        return view('admin.box.index', compact('data'));
    }

    public function listSection(Request $request)
    {
        $data = BranchBox::query()
            ->when($request->keyword, function ($q) use ($request) {
                return $q->where('name', $request->keyword);
            })
            ->where('branch_id','!=',null)
            ->with('branch')
            ->get();
        return view('admin.box_section.index', compact('data'));
    }

    public function showSection($id)
    {
        $data = BranchBox::query()->find($id);
        $branches = Branch::all();
        return view('admin.box.edit', compact('data', 'branches'));
    }

    public function show($id)
    {
        $data = BranchBox::query()->find($id);
        return view('admin.box_section.edit', compact('data'));
    }

    public function edit(BoxEditRequest $request, $id)
    {
        $branchBox = BranchBox::query()->find($id);
        $branchBox->update($request->validated());
        if($request->post('branch_id') == null){
            return redirect()->route('branch.boxes.box.list')->with('success', 'წარმატებით განახლდა');
        }
        return redirect()->route('branch.boxes.box.listSection')->with('success', 'წარმატებით განახლდა');
    }

    public function remove($id)
    {
        BranchBox::destroy($id);
        return redirect()->route('branch.boxes.box.list')->with('success', 'წარმატებით წაიშალა');
    }

    public function addContainer(AddContainerRequest $request)
    {
        $good = goods::query()->where('tracking_code', $request->post('tracking_code'))->first();

        return BranchBoxContainer::query()->create([
            'branch_box_delivery_id' => $request->post('branch_box_delivery_id'),
            'good_id' => $good->id,
            'flight_id' => $good->flight_id,
        ])->load(['good.flight', 'delivery.box', 'delivery.branch']);
    }

    public function deleteContainer($id): JsonResponse
    {
        $deleted = BranchBoxContainer::destroy($id);

        return response()->json([], $deleted?200:404);
    }

    public function boxFill(BoxFillRequest $request)
    {
        $delivery = BranchBoxDelivery::query()
            ->where('status_id', 0)
            ->where('branch_id', $request->post('branch_id'))
            ->where('branch_box_id', $request->post('branch_box_id'))
            ->first();
        if (!$delivery) {
            $delivery = BranchBoxDelivery::query()->create($request->validated());
        }
        $good = goods::query()->where('tracking_code', $request->post('tracking_code'))->first();
        $good->update(['small_comment' => 'SCANNED']);

        if (!$delivery->container->where('good_id', $good->id)->first()) {
            $delivery->container()->updateOrCreate([
                'flight_id' => $good->flight_id,
                'good_id' => $good->id
            ]);
        }
//        $branches = Branch::all();
//        $boxes = BranchBox::where('branch_id', null)->get();
//        $flights = Flight::with('parcelCoordinates')->where('flight_parcel_state', 'DELIVERED_OK')->where('created_at', '>', '2023-09-01 18:58:00')->orderByDesc('updated_at')->get();
        return $delivery->load(['container.good.flight']);
//        return $deliverys;
//        return view('admin.box_delivery.create', compact('branches', 'boxes', 'flights', 'deliverys'));
    }

    public function deliveryList(Request $request)
    {
        $statusId = $request->status_id;
        $data = BranchBoxDelivery::query()
            ->when($statusId, function ($q) use ($statusId){
                $q->where('status_id', $statusId);
            })
            ->with(['container', 'box', 'container.good' => function($query){
                $query->select('id', 'flight_id');
            },'container.good.flight' => function($query){
                $query->select('id', 'flight_number');
            }])
            ->orderByDesc('id')
            ->get();
        $drivers = Driver::all();
        $statuses = BranchBoxDeliveryStatus::all();
        $branches = Branch::all();


        return view('admin.box_delivery.index', compact('data', 'statuses', 'drivers', 'branches'));
    }

    public function deliveryCreate(Request $request)
    {
        $branches = Branch::all();
        $boxes = BranchBox::where('branch_id', null)->get();
        $flights = Flight::with('parcelCoordinates')
            ->where('flight_parcel_state', 'DELIVERED_OK')
            ->orWhere('flight_parcel_state', 'SENT')
            ->where('created_at', '>', '2023-09-01 18:58:00')
            ->orderByDesc('updated_at')
            ->get();
        $container = BranchBoxDelivery::query()->where('id', $request->get('container_id'))->first();
        $deliverys = 0;
        $drivers = Driver::all();

        return view('admin.box_delivery.create', compact('branches', 'boxes', 'flights','container','deliverys', 'drivers'));
    }

    public function showDelivery($id)
    {
//        $t = BranchBoxContainer::query()->create([
//            'branch_box_delivery_id' => 16,
//            'good_id' => 240869,
//            'flight_id' => 749,
//        ])->load('good.flight', 'delivery.box');
//        dd($t);
        $branches = Branch::all();
        $boxDelivery = BranchBoxDelivery::query()
            ->with(['container', 'box'])
            ->find($id);
        $boxes = BranchBox::query()->where('branch_id', null)->get();
        $flights = Flight::with('parcelCoordinates')->where('flight_parcel_state', 'DELIVERED_OK')->where('created_at', '>', '2023-09-01 18:58:00')->orderByDesc('updated_at')->get();

        return view('admin.box_delivery.edit', compact('boxDelivery', 'boxes', 'flights', 'branches'));
    }

    public function editDelivery(DeliveryEditRequest $request)
    {
        $delivery = BranchBoxDelivery::query()->with('driver')->find($request->post('id'));
        $readyForDeliverStatus = BranchBoxDeliveryStatus::query()
            ->where('slug', 'ready_to_delivery')
            ->first();
        if ($request->status_id == $readyForDeliverStatus->id and $delivery->status_id != $readyForDeliverStatus->id){
            // send sms

            $smsText = $delivery->box->name.' გასაგზავნად მზად არის - '.$delivery->branch->title_ge;
            (new SMS($delivery->driver->mobile, $smsText))->send();

        }

        $delivery->update($request->validated());
        return $delivery->load('container');
    }

    public function deleteDelivery($id)
    {
        BranchBoxDelivery::destroy($id);
        return redirect()->route('branch.boxes.box.delivery.list')->with('success', 'წარმატებით წაიშალა');
    }

    public function deleteBoxContainer($goodId)
    {
        return BranchBoxContainer::query()->where('good_id', $goodId)->delete();
    }

//    public function flightContainer(ContainerRequest $request)
//    {
//        $containerGoodIds = BranchBoxContainer::query()
//            ->when($request->get('branch_id'), function ($q) use ($request) {
//                return $q->where('branch_id', $request->get('branch_id'));
//            })
//            ->where('flight_id', $request->get('flight_id'))
//            ->pluck('good_id');
//
//        $goods = goods::query();
//
//        $request->get('transform') == 1
//            ? $goods->whereNotIn('id', $containerGoodIds)
//            : $goods->whereIn('id', $containerGoodIds);
//
//        return $goods->paginate(10);
//    }

    public function boxes($id)
    {
        $boxes = BranchBox::query()
            ->when($id, function ($box) use ($id){
                $box->where('branch_id', $id);
            })
            ->get();

        return response()->json($boxes);
    }

    public function localDistributionForm(Request $request)
    {
        $branchBoxId = $request->get('branch_box_id');
        $branches = Branch::all();

        $box = BranchBox::query()->find($branchBoxId);

        return view('admin.local_distribution.create', compact('branches', 'box'));
    }

    public function localDistributionCreate(CreateRequest $request)
    {
        $branchId = $request->branch_id;

        $branchBox = BranchBox::query()
            ->where('id', $request->branch_box_id)
            ->when($branchId, function ($box) use ($branchId){
                $box->where('branch_id', $branchId);
            })
            ->first();

        $good = goods::query()
            ->where('tracking_code', $request->tracking_code)
//            ->where('branch_id', $branchBox->branch_id)
            ->first()
        ;

        if (!$good)
        {
            return response()->json([
                'custom_errors' => 'ტრეკინგ კოდი არასწორია.'
            ],404);
        }

        if($request->is_chartering == 'true') {
            $exists = LocalDistribution::query()
                ->where('good_id', $good->id)
    //            ->where('branch_box_id', $request->branch_box_id)
                ->first()
            ;
            if($exists and $exists->branch_box_id != $request->branch_box_id)
            {
                $branchBox = BranchBox::query()->find($exists->branch_box_id);
                $branchTitle = $branchBox->branch->title_ge ?? 'უცნობ';
                return response()->json([
                    'custom_errors' => 'ამანათი ეკუთვნის "'.$branchTitle.'", თარო: '.($branchBox->name ?? 'უცნობ'),
                    'sound_type' => 'not_exists_on_current_shelf'
                ],404);
            }

            if ($branchBox->branch_id != $good->branch_id)
            {
                $branchName = $good->Branch->title_ge;
                return response()->json([
                    'custom_errors' => "ამანათი ეკუთვნის $branchName -ს ფილიალს",
                    'sound_type' => 'diff_branch',
                ],404);
            }

            return response()->json([
                'custom_errors' => "ამანათი მოიძებნა",
                'is_chartering' => true
            ],200);
        }

        if (
            LocalDistribution::query()
                ->where('good_id', $good->id)
                ->where('branch_box_id', $request->branch_box_id)
                ->exists()
        ){
            return response()->json([
                'custom_errors' => "ამანათი დასკანერებულია",
                'scanned' => true
            ],404);
        }

        if($good->branch_id != $branchBox->branch_id)
        {
            return response()->json([
                'custom_errors' => "ამანათი ეკუთვნის - ".$good->branch->title_ge . ' ფილიალს',
                'branch_error' => true
            ],404);
        }

        $distribution = LocalDistribution::query()
            ->updateOrCreate([
                'good_id' => $good->id,
            ],[
                'branch_box_id' => $request->branch_box_id
            ]);

        goods::query()->find($good->id)->update(['small_comment' => 'SCANNED']);


        $branchLog = UserBranchChangeLog::query()
            ->where('tracking_code', $request->tracking_code)
            ->first()
        ;

        if($branchLog)
        {
            $text = 'შეცვლილ ფილიალში ამანათი ჩავიდა';
            (new SMS($branchLog->user->phone, $text))->send();
        }

        return response()->json($distribution->load(['good.User','good.Branch','branchBox.branch']));
    }

    public function localDistributionList(Request $request)
    {
        $branchId = $request->branch_id;

        $branches = Branch::all();

        $branchBoxes = BranchBox::query()
            ->with(['limitedLocalDistribution','branch', 'limitedLocalDistribution.good' => function ($query) {
                $query->select('id', 'flight_id'); // Replace 'id', 'column1', 'column2' with the columns you want to retrieve from the 'good' table
            }, 'limitedLocalDistribution.good.flight' => function ($query) {
                $query->select('id', 'flight_number'); // Replace 'id' with the primary key of the flight table
            }])
            ->when($branchId, function ($distribution) use ($branchId){
                $distribution->where('branch_id', $branchId);
            })
            ->whereNotNull('branch_id')
            ->get()
        ;

        return view('admin.local_distribution.index', compact('branchBoxes', 'branches'));

    }

    public function localDistributionShow($id) // branch box id
    {
        $rowDataFirst = LocalDistribution::query()
            ->with(['branchBox.branch', 'good.Branch', 'good' => function($good){
                $good->select(['id', 'tracking_code', 'rec_name', 'room_number', 'branch_id','user_id']);
            }])
            ->where('status_id', 1)
            ->whereHas('good', function ($good){
                $good->where('flight_parcel_state', '!=','TOOK_OUT');
            })
            ->where('branch_box_id', $id);
        $rowData= $rowDataFirst->get()->sortByDesc('id');;
        $branchId = $rowDataFirst->first()?->branchBox?->branch_id;

        return view('admin.local_distribution.edit', compact('rowData', 'id','branchId'));
    }

    public function localDistributionEdit(EditRequest $request)
    {
        $distribution = LocalDistribution::query()->find($request->post('id'));
        $distribution->update($request->validated());

        return $distribution->load('good');
    }

    public function localDistributionRemove($id)
    {
        $removed = LocalDistribution::destroy($id);

        return $removed
            ? redirect()->back()->with('success', 'წარმატებით წაიშალა')
            : redirect()->back()->with('error', 'მოხდა შეცდომა')
        ;
    }

    public function sectionQr($id)
    {
        $url = route('branch.boxes.local.distribution.create.form').'?branch_box_id='.urlencode($id);

        // Build the QR code with a label
        $result = Builder::create()
            ->writer(new PngWriter())
            ->data($url)
//            ->encoding(new Encoding('UTF-8'))
            ->encoding(new Encoding('ISO-8859-1'))
            ->size(300)
            ->margin(10)
            ->foregroundColor(new Color(0, 0, 0))
            ->backgroundColor(new Color(255, 255, 255))
            ->labelText('Branch Box: ' . BranchBox::query()->find($id)?->name ?? 'not found!') // Add text below the QR code
            ->labelFont(new NotoSans(15)) // Set font and size for the label
            ->build();

        // Return the generated QR code with text as an image file response
        return response($result->getString(), 200)
            ->header('Content-Type', 'image/png')
            ->header('Content-Disposition', 'attachment; filename="qrcode_box_' . $id . '.png"');
    }

    public function boxByName(Request $request): JsonResponse
    {
        $request->validate(['name' => 'required']);

        $branchBox = BranchBox::query()->where('name', $request->get('name'))->first();

        return response()->json($branchBox, $branchBox ? 200 : 404);
    }
}
