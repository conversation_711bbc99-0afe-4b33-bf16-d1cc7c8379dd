<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<!--begin::Head-->
@include("partials.admin.head")
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body"
    class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize-hoverable page-loading">
    <!--begin::Main-->
    @yield("modal")
    <!--begin::Header Mobile-->
    <div id="kt_header_mobile" class="header-mobile align-items-center  header-mobile-fixed ">
        <!--begin::Logo-->

        <!--end::Logo-->
        <!--begin::Toolbar-->
        <div class="d-flex align-items-center">
            <!--begin::Aside Mobile Toggle-->
            <button class="btn p-0 burger-icon burger-icon-left" id="kt_aside_mobile_toggle">
                <span></span>
            </button>
            <!--end::Aside Mobile Toggle-->
            <!--begin::Header Menu Mobile Toggle-->
            <button class="btn p-0 burger-icon ml-4" id="kt_header_mobile_toggle">
                <span></span>
            </button>
            <!--end::Header Menu Mobile Toggle-->
            <!--begin::Topbar Mobile Toggle-->
            <button class="btn btn-hover-text-primary p-0 ml-2" id="kt_header_mobile_topbar_toggle">
                <span class="svg-icon svg-icon-xl">
                    <!--begin::Svg Icon | path:assets/media/svg/icons/General/User.svg--><svg
                        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                        height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24" />
                            <path
                                d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z"
                                fill="#000000" fill-rule="nonzero" opacity="0.3" />
                            <path
                                d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z"
                                fill="#000000" fill-rule="nonzero" />
                        </g>
                    </svg>
                    <!--end::Svg Icon--></span> </button>
            <!--end::Topbar Mobile Toggle-->
        </div>
        <!--end::Toolbar-->
    </div>
    <!--end::Header Mobile-->
    <div class="d-flex flex-column flex-root">
        <!--begin::Page-->
        <div class="d-flex flex-row flex-column-fluid page">
        @include("partials.admin.aside")
            <!-------------------------------------------begin::Aside----------------------------------------->
            <!--end::Aside-->

            <!--begin::Wrapper-->
            <div class="d-flex flex-column flex-row-fluid wrapper" id="kt_wrapper">
                <!--begin::Header-->
                <div id="kt_header" class="header  header-fixed ">
                    <!--begin::Container-->
                    <div class=" container-fluid  d-flex align-items-stretch justify-content-between">
                        <!--begin::Header Menu Wrapper-->
                        <div class="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">
                        </div>
                        <!--end::Header Menu Wrapper-->

                        <!--begin::Topbar-->
                        <div class="topbar">
                            <!--begin::User-->
                            <div class="topbar-item">
                                <div class="btn btn-icon btn-icon-mobile w-auto btn-clean d-flex align-items-center btn-lg px-2"
                                    id="kt_quick_user_toggle">
                                    <span
                                        class="text-muted font-weight-bold font-size-base d-none d-md-inline mr-1">Hi,</span>
                                    <span
                                        class="text-dark-50 font-weight-bolder font-size-base d-none d-md-inline mr-3">
                                        admin
                                    </span>

                                </div>
                                <div class="mt-2">
                                    <a class="btn btn-sm btn-light-primary font-weight-bolder py-2 px-5" href="{{ route('admin.logout') }}"
                                       onclick="event.preventDefault();
                                             document.getElementById('logout-form').submit();">
                                        <span class="geo">
                                        @lang('trans.sign_out')
                                        </span>
                                    </a>
                                    <form id="logout-form" action="{{ route('admin.logout') }}" method="POST" style="display: none;">
                                        @csrf
                                    </form>

                                </div>
                            </div>
                            <!--end::User-->
                        </div>
                        <!--end::Topbar-->
                    </div>
                    <!--end::Container-->
                </div>
                <!--end::Header-->

                <!--begin::Content.blade.php-->
                <div class="content  d-flex flex-column flex-column-fluid" id="kt_content">
                    <!--begin::Subheader-->

                    <!--end::Subheader-->
{{--                        -----------------------------CONTENT START------------------------------------------}}
                    <!--begin::Entry-->
                @yield('content')


                    <!--end::Entry-->
                </div>
                <!--end::Content.blade.php-->

                <!----------------------------------begin::Footer------------------------------------------------------>
{{--                <div class="footer bg-white py-4 d-flex flex-lg-column " id="kt_footer">--}}
{{--                    <!--begin::Container-->--}}
{{--                    <div--}}
{{--                        class=" container-fluid  d-flex flex-column flex-md-row align-items-center justify-content-between">--}}
{{--                        <!--begin::Copyright-->--}}
{{--                        <div class="text-dark order-2 order-md-1">--}}
{{--                            <span class="text-muted font-weight-bold mr-2">2020&copy;</span>--}}
{{--                            <a href="http://keenthemes.com/metronic" target="_blank"--}}
{{--                                class="text-dark-75 text-hover-primary">Goldex</a>--}}
{{--                        </div>--}}
{{--                        <!--end::Copyright-->--}}

{{--                        <!--begin::Nav-->--}}
{{--                        <div class="nav nav-dark">--}}
{{--                  sad          <a href="http://keenthemes.com/metronic" target="_blank"--}}
{{--                                class="nav-link pl-0 pr-5">About</a>--}}
{{--                            <a href="http://keenthemes.com/metronic" target="_blank" class="nav-link pl-0 pr-5">Team</a>--}}
{{--                            <a href="http://keenthemes.com/metronic" target="_blank"--}}
{{--                                class="nav-link pl-0 pr-0">Contact</a>--}}
{{--                        </div>--}}
{{--                        <!--end::Nav-->--}}
{{--                    </div>--}}
{{--                    <!--end::Container-->--}}
{{--                </div>--}}
                <!---------------------------------end::Footer---------------------------------------------------------->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Page-->
    </div>
    <!--end::Main-->





    <!-- begin::User Panel-->

    <!-- end::User Panel-->


    <!--begin::Quick Cart-->
    <div id="kt_quick_cart" class="offcanvas offcanvas-right p-10">
        <!--begin::Header-->
        <div class="offcanvas-header d-flex align-items-center justify-content-between pb-7">
            <h4 class="font-weight-bold m-0">
                Shopping Cart
            </h4>
            <a href="#" class="btn btn-xs btn-icon btn-light btn-hover-primary" id="kt_quick_cart_close">
                <i class="ki ki-close icon-xs text-muted"></i>
            </a>
        </div>
        <!--end::Header-->

        <!--begin::Content.blade.php-->

        <!--end::Content.blade.php-->
    </div>
    <!--end::Quick Cart-->

    <!--begin::Quick Panel-->
    <div id="kt_quick_panel" class="offcanvas offcanvas-right pt-5 pb-10">
        <!--begin::Header-->
        <div class="offcanvas-header offcanvas-header-navs d-flex align-items-center justify-content-between mb-5">
            <ul class="nav nav-bold nav-tabs nav-tabs-line nav-tabs-line-3x nav-tabs-primary flex-grow-1 px-10"
                role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#kt_quick_panel_logs">Audit Logs</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#kt_quick_panel_notifications">Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#kt_quick_panel_settings">Settings</a>
                </li>
            </ul>
            <div class="offcanvas-close mt-n1 pr-5">
                <a href="#" class="btn btn-xs btn-icon btn-light btn-hover-primary" id="kt_quick_panel_close">
                    <i class="ki ki-close icon-xs text-muted"></i>
                </a>
            </div>
        </div>
        <!--end::Header-->

        <!--begin::Content.blade.php-->

        <!--end::Content.blade.php-->
    </div>
    <!--end::Quick Panel-->

    <!--begin::Chat Panel-->

    <!--end::Chat Panel-->

    <!--begin::Scrolltop-->
    <div id="kt_scrolltop" class="scrolltop">
        <span class="svg-icon">
            <!--begin::Svg Icon | path:assets/media/svg/icons/Navigation/Up-2.svg--><svg
                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <polygon points="0 0 24 0 24 24 0 24" />
                    <rect fill="#000000" opacity="0.3" x="11" y="10" width="2" height="10" rx="1" />
                    <path
                        d="M6.70710678,12.7071068 C6.31658249,13.0976311 5.68341751,13.0976311 5.29289322,12.7071068 C4.90236893,12.3165825 4.90236893,11.6834175 5.29289322,11.2928932 L11.2928932,5.29289322 C11.6714722,4.91431428 12.2810586,4.90106866 12.6757246,5.26284586 L18.6757246,10.7628459 C19.0828436,11.1360383 19.1103465,11.7686056 18.7371541,12.1757246 C18.3639617,12.5828436 17.7313944,12.6103465 17.3242754,12.2371541 L12.0300757,7.38413782 L6.70710678,12.7071068 Z"
                        fill="#000000" fill-rule="nonzero" />
                </g>
            </svg>
            <!--end::Svg Icon--></span></div>
    <!--end::Scrolltop-->

    <!--begin::Sticky Toolbar-->

    <!--end::Sticky Toolbar-->
    <!--begin::Demo Panel-->

    <!--end::Demo Panel-->

    <script>var HOST_URL = "https://preview.keenthemes.com/metronic/theme/html/tools/preview";</script>
    <!--begin::Global Config(global config for global JS scripts)-->
    <script>
        var KTAppSettings = {
            "breakpoints": {
                "sm": 576,
                "md": 768,
                "lg": 992,
                "xl": 1200,
                "xxl": 1400
            },
            "colors": {
                "theme": {
                    "base": {
                        "white": "#ffffff",
                        "primary": "#3699FF",
                        "secondary": "#E5EAEE",
                        "success": "#1BC5BD",
                        "info": "#8950FC",
                        "warning": "#FFA800",
                        "danger": "#F64E60",
                        "light": "#E4E6EF",
                        "dark": "#181C32"
                    },
                    "light": {
                        "white": "#ffffff",
                        "primary": "#E1F0FF",
                        "secondary": "#EBEDF3",
                        "success": "#C9F7F5",
                        "info": "#EEE5FF",
                        "warning": "#FFF4DE",
                        "danger": "#FFE2E5",
                        "light": "#F3F6F9",
                        "dark": "#D6D6E0"
                    },
                    "inverse": {
                        "white": "#ffffff",
                        "primary": "#ffffff",
                        "secondary": "#3F4254",
                        "success": "#ffffff",
                        "info": "#ffffff",
                        "warning": "#ffffff",
                        "danger": "#ffffff",
                        "light": "#464E5F",
                        "dark": "#ffffff"
                    }
                },
                "gray": {
                    "gray-100": "#F3F6F9",
                    "gray-200": "#EBEDF3",
                    "gray-300": "#E4E6EF",
                    "gray-400": "#D1D3E0",
                    "gray-500": "#B5B5C3",
                    "gray-600": "#7E8299",
                    "gray-700": "#5E6278",
                    "gray-800": "#3F4254",
                    "gray-900": "#181C32"
                }
            },
            "font-family": "Poppins"
        };
    </script>
    <!--end::Global Config-->

    <!--begin::Global Theme Bundle(used by all pages)-->

    <script src="{{asset("assets/plugins/global/plugins.bundle.js?v=7.0.6")}}"></script>
    <script src="{{asset("assets/plugins/custom/prismjs/prismjs.bundle.js?v=7.0.6")}}"></script>
    <script src="{{asset("assets/js/scripts.bundle.js?v=7.0.6")}}"></script>
    <!--end::Global Theme Bundle-->

    <!--begin::Page Vendors(used by this page)-->
    <script src="{{asset("assets/plugins/custom/fullcalendar/fullcalendar.bundle.js?v=7.0.6")}}"></script>
{{--    <script src="//maps.google.com/maps/api/js?key=AIzaSyBTGnKT7dt597vo9QgeQ7BFhvSRP4eiMSM?v=7.0.6"></script>--}}
{{--    <script src="{{asset("assets/plugins/custom/gmaps/gmaps.js?v=7.0.6")}}"></script>--}}
    <!--end::Page Vendors-->

    <!--begin::Page Scripts(used by this page)-->
    <script src="{{asset("assets/js/pages/widgets.js?v=7.0.6")}}"></script>

    <script src="{{asset("assets/js/sweetalert2.all.min.js")}}"></script>
    @if(session()->has("success"))
        <script>
            $.notify({
                // options
                message: '{{session()->get("success")}}'
            },{
                // settings
                type: 'success'
            });
        </script>
    @endif
    @if(session()->has("error"))
        <script>
            $.notify({
                message: '{{ session()->get("error") }}'
            },{
                type: 'danger'
            });
        </script>
    @endif
    @if(session()->has("success_room"))
        <script>
            $.notify({
                // options
                message: '{{session()->get("success_room")['message']}}'
            },{
                // settings
                type: 'success'
            });
        </script>
    @endif
    <!--end::Page Scripts-->
{{--    @foreach(config('layout.resources.js') as $script)--}}
{{--        <script src="{{ asset($script) }}" type="text/javascript"></script>--}}
{{--    @endforeach--}}
    @yield('scripts')

    @livewireScripts


    @if(auth()->user()->alert_id == 1)
        <audio id="success">
            <source src="{{asset('sounds/second_group/success.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="error">
            <source src="{{asset('sounds/second_group/error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="branch-error">
            <source src="{{asset('sounds/second_group/branch-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="last">
            <source src="{{asset('sounds/second_group/last.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="scanned-error">
            <source src="{{asset('sounds/second_group/scanned-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="took-out">
            <source src="{{asset('sounds/second_group/scanned-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="not-found-error">
            <source src="{{asset('sounds/second_group/not-found-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="not-current-box">
            <source src="{{asset('sounds/first_group/not-current-box.mp3')}}" type="audio/mpeg">
        </audio><audio id="on_way">
            <source src="{{asset('sounds/first_group/on_way.mp3')}}" type="audio/mpeg">
        </audio>
    @else

        <audio id="success">
            <source src="{{asset('sounds/first_group/success.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="error">
            <source src="{{asset('sounds/first_group/error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="branch-error">
            <source src="{{asset('sounds/first_group/branch-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="last">
            <source src="{{asset('sounds/first_group/last.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="scanned-error">
            <source src="{{asset('sounds/first_group/scanned-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="took-out">
            <source src="{{asset('sounds/first_group/scanned-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="not-found-error">
            <source src="{{asset('sounds/first_group/not-found-error.mp3')}}" type="audio/mpeg">
        </audio>
        <audio id="not-current-box">
            <source src="{{asset('sounds/first_group/not-current-box.mp3')}}" type="audio/mpeg">
        </audio>
        </audio><audio id="on_way">
            <source src="{{asset('sounds/first_group/on_way.mp3')}}" type="audio/mpeg">
        </audio>
    @endif

</body>
<script>
    function playNotificationSound(soundName){

        var sound = document.getElementById(soundName);

        if (!sound.paused) {
            sound.pause();
            sound.currentTime = 0;
        }
        sound.play();
    }
</script>
<!--end::Body-->

</html>
